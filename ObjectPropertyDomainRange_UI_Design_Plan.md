# ObjectPropertyDefinition 域和范围扩展UI设计计划

## 总体目标

为 `ObjectPropertyDefinition` 的 `domains` 和 `ranges` 字段提供一个直观、高效且支持复杂 `IClassExpression` 结构编辑的Unity编辑器UI。

## 核心设计理念

1.  **统一入口，分步配置：** 通过一个统一的“添加表达式”按钮进入模态窗口，在该窗口内通过Tab页选择和配置不同类型的类表达式。
2.  **层级可视化：** 对于嵌套的类表达式（如联合、交集、限制），采用可折叠的面板或树状结构清晰展示其层级关系。
3.  **动态适应：** UI根据所选的类表达式类型或限制类型动态调整，只显示相关输入字段，减少视觉噪音。
4.  **直观显示：** 利用 `IClassExpression.GetDisplayName()` 方法，在列表和层级结构中提供清晰的表达式概览。

## Mermaid 流程图：添加/编辑类表达式

```mermaid
graph TD
    A[ObjectPropertyDefinition Inspector] --> B{点击 "添加表达式" 或 "编辑" 按钮};
    B -- 添加 --> C{弹出 "类表达式编辑器" 窗口};
    B -- 编辑 --> C;
    
    C -- Tab 导航 --> D1[概念表达式];
    C -- Tab 导航 --> D2[联合表达式];
    C -- Tab 导航 --> D3[交集表达式];
    C -- Tab 导航 --> D4[限制表达式];

    D1 --> E1[概念选择器 (ObjectField/搜索框)];
    
    D2 --> E2[联合表达式配置区];
    E2 --> E2_1[子表达式列表 (ListView)];
    E2_1 -- 每个子项可编辑/删除 --> C; %% 递归编辑子表达式
    E2 --> E2_2[添加子表达式按钮];
    E2_2 --> C; %% 递归添加子表达式

    D3 --> E3[交集表达式配置区];
    E3 --> E3_1[子表达式列表 (ListView)];
    E3_1 -- 每个子项可编辑/删除 --> C; %% 递归编辑子表达式
    E3 --> E3_2[添加子表达式按钮];
    E3_2 --> C; %% 递归添加子表达式

    D4 --> F1[属性选择器 (ObjectField)];
    D4 --> F2[限制类型选择器 (EnumField)];
    F2 -- 根据类型动态显示 --> G1[目标类表达式选择器 (弹出 C)];
    F2 -- 根据类型动态显示 --> G2[值输入框 (TextField/类型化输入)];
    F2 -- 根据类型动态显示 --> G3[基数输入框 (IntegerField)];

    C -- 确认 --> H[更新 ObjectPropertyDefinition];
    C -- 取消 --> I[关闭窗口];
```

## Mermaid 类图：UI 结构概念

```mermaid
classDiagram
    direction LR
    class ObjectPropertyDefinitionEditor {
        +OnInspectorGUI()
        -DrawClassExpressionList(SerializedProperty listProperty, string label)
        -ShowClassExpressionEditorWindow(IClassExpression currentExpression, Action<IClassExpression> onConfirm)
    }

    class ClassExpressionEditorWindow {
        +static Show(IClassExpression currentExpression, Action<IClassExpression> onConfirm)
        -VisualElement rootVisualElement
        -TabbedPanel tabbedPanel
        -ConceptExpressionPanel conceptPanel
        -LogicalExpressionPanel unionPanel
        -IntersectionExpressionPanel intersectionPanel
        -RestrictionExpressionPanel restrictionPanel
        -Button confirmButton
        -Button cancelButton
    }

    class ConceptExpressionPanel {
        +Bind(IClassExpression expression)
        -ObjectField conceptDefinitionField
    }

    class LogicalExpressionPanel {
        +Bind(IClassExpression expression)
        -ListView operandsListView
        -Button addOperandButton
        -Button removeOperandButton
    }

    class RestrictionExpressionPanel {
        +Bind(IClassExpression expression)
        -ObjectField propertyField
        -EnumField restrictionTypeField
        -VisualElement dynamicInputContainer
        -ObjectField targetClassExpressionField
        -TextField propertyValueJsonField
        -IntegerField cardinalityValueField
    }

    ObjectPropertyDefinitionEditor ..> ClassExpressionEditorWindow : opens
    ClassExpressionEditorWindow ..> ConceptExpressionPanel
    ClassExpressionEditorWindow ..> LogicalExpressionPanel
    ClassExpressionEditorWindow ..> RestrictionExpressionPanel
    LogicalExpressionPanel ..> ClassExpressionEditorWindow : recursively opens
```

## 详细计划步骤

1.  **创建 `ObjectPropertyDefinitionEditor` (自定义 Inspector)**
    *   **文件路径：** `Assets/Poesis/Ontology/Editor/ObjectPropertyDefinitionEditor.cs`
    *   **功能：**
        *   使用 `[CustomEditor(typeof(ObjectPropertyDefinition))]` 标记，接管 `ObjectPropertyDefinition` 的 Inspector 绘制。
        *   在 `OnInspectorGUI()` 中，为 `domains` 和 `ranges` 列表绘制自定义UI。
        *   每个列表项：
            *   显示 `IClassExpression.GetDisplayName()` 作为标题。
            *   提供“编辑”按钮：点击后弹出 `ClassExpressionEditorWindow`，并传入当前表达式进行编辑。
            *   提供“删除”按钮：直接从列表中移除该表达式。
        *   列表下方提供一个“添加表达式”按钮：点击后弹出 `ClassExpressionEditorWindow`，用于创建新的表达式。
        *   **数据迁移：** 在 `OnEnable()` 或 `OnInspectorGUI()` 中实现数据迁移逻辑，将旧的 `ConceptDefinition` 转换为 `ConceptDefinitionClassExpression`。可以考虑在 Inspector 中提供一个“迁移旧数据”按钮，或者在检测到旧数据时自动执行并提示用户保存。

2.  **创建 `ClassExpressionEditorWindow` (模态编辑窗口)**
    *   **文件路径：** `Assets/Poesis/Ontology/Editor/ClassExpressions/ClassExpressionEditorWindow.cs`
    *   **功能：**
        *   继承 `UnityEditor.EditorWindow`，用于创建独立的模态窗口。
        *   **UXML 布局：** 使用 UXML 定义窗口的整体布局，包括一个 `TabbedPanel` 和底部的“确定”、“取消”按钮。
        *   **Tab 页：** 每个Tab页对应一种 `IClassExpression` 类型（概念、联合、交集、限制），并加载相应的 `VisualElement` 面板。
        *   **数据绑定：** 窗口打开时，根据传入的 `IClassExpression` 类型，自动切换到对应的Tab页并填充数据。
        *   **确认/取消：** “确定”按钮点击时，从当前激活的Tab页获取配置好的 `IClassExpression` 实例，并通过回调 (`Action<IClassExpression> onConfirm`) 返回给调用者（`ObjectPropertyDefinitionEditor`）。“取消”按钮关闭窗口。

3.  **创建各个类表达式的 UI 面板 (VisualElement)**
    *   为每种 `IClassExpression` 类型创建独立的 `VisualElement` 类和对应的 UXML 布局文件。
    *   **`ConceptExpressionPanel.cs`**
        *   **文件路径：** `Assets/Poesis/Ontology/Editor/ClassExpressions/ConceptExpressionPanel.cs`
        *   **UXML：** `Assets/Poesis/Ontology/Editor/ClassExpressions/ConceptExpressionPanel.uxml`
        *   **功能：** 包含一个 `ObjectField`，允许用户拖拽或选择 `ConceptDefinition` 资产。
    *   **`LogicalExpressionPanel.cs` (用于 `UnionClassExpression` 和 `IntersectionClassExpression`)**
        *   **文件路径：** `Assets/Poesis/Ontology/Editor/ClassExpressions/LogicalExpressionPanel.cs`
        *   **UXML：** `Assets/Poesis/Ontology/Editor/ClassExpressions/LogicalExpressionPanel.uxml`
        *   **功能：**
            *   包含一个 `ListView`，用于显示 `operands` 列表。
            *   `ListView` 的每个项：
                *   显示子表达式的 `GetDisplayName()`。
                *   提供“编辑”按钮：点击后再次弹出 `ClassExpressionEditorWindow`，实现递归编辑。
                *   提供“删除”按钮。
            *   “添加子表达式”按钮：点击后弹出 `ClassExpressionEditorWindow`，用于创建新的子表达式并添加到 `operands` 列表中。
    *   **`RestrictionExpressionPanel.cs`**
        *   **文件路径：** `Assets/Poesis/Ontology/Editor/ClassExpressions/RestrictionExpressionPanel.cs`
        *   **UXML：** `Assets/Poesis/Ontology/Editor/ClassExpressions/RestrictionExpressionPanel.uxml`
        *   **功能：**
            *   `ObjectField` 用于选择 `PropertyDefinition` (可以是 `ObjectPropertyDefinition` 或 `DataPropertyDefinition`)。
            *   `EnumField` 用于选择 `RestrictionType`。
            *   一个 `VisualElement` 作为动态内容容器。根据 `RestrictionType` 的选择，动态地向此容器添加或移除以下输入字段：
                *   `SomeValuesFrom`, `AllValuesFrom`: `ObjectField` 或自定义选择器，用于选择 `IClassExpression` (点击后再次弹出 `ClassExpressionEditorWindow`)。
                *   `HasValue`: `TextField`。考虑到 `propertyValueJson` 的通用性，可以先用 `TextField` 输入JSON字符串。未来可以根据 `PropertyDefinition` 的 `ValueType` 提供更智能的类型化输入（例如，如果是 `int` 就显示 `IntegerField`，如果是 `bool` 就显示 `Toggle`）。
                *   `MinCardinality`, `MaxCardinality`, `ExactCardinality`: `IntegerField`。

4.  **UXML 和 USS 文件**
    *   为上述所有 `VisualElement` 面板创建对应的 UXML 布局文件。
    *   创建 USS 样式文件 (`Assets/Poesis/Ontology/Editor/ClassExpressions/ClassExpressionEditor.uss`)，用于统一窗口和面板的样式，确保美观和一致性。